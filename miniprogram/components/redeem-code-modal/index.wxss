/* 兑换码模态框样式 */
@import "../../styles/modal-animations.wxss";

.redeem-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;

  /* 使用统一的transition动画 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.redeem-modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.redeem-modal-content {
  background: white;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);

  /* 使用统一的transition动画 */
  transform: scale(0.9) translateY(50rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.redeem-modal-overlay.show .redeem-modal-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.title-icon {
  font-size: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  background: #f5f5f5;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: #e9ecef;
}

.close-icon {
  font-size: 24rpx;
  color: #666;
}

/* 模态框主体 */
.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 输入区域 */
.input-section {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.input-wrapper {
  position: relative;
}

.code-input {
  width: auto;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 2rpx;
  text-transform: uppercase;
  background: #fafafa;
  transition: all 0.3s ease;
}

.code-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
  text-align: center;
}

/* 获取兑换码 */
.get-code-section {
  margin-bottom: 32rpx;
}

.get-code-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 16rpx;
}

.get-code-btn {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.get-code-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.btn-text {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.btn-arrow {
  font-size: 32rpx;
  color: white;
  font-weight: 300;
}

/* 使用说明 */
.tips-section {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
}

.tips-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.tips-title::before {
  content: '💡';
  font-size: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.store-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 8rpx;
}

.store-btn:active {
  transform: scale(0.98);
}

.store-btn .btn-icon {
  font-size: 24rpx;
}

.store-btn .btn-text {
  font-size: 26rpx;
  font-weight: 600;
  color: white;
}

.store-btn .btn-arrow {
  font-size: 24rpx;
  color: white;
  font-weight: 300;
}

.redeem-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.redeem-btn:active {
  transform: scale(0.98);
}

.redeem-btn[disabled] {
  background: #e9ecef;
  color: #adb5bd;
  box-shadow: none;
  transform: none;
}
